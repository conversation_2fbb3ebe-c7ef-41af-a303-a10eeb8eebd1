const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Set viewport to full HD
  await page.setViewport({ width: 1920, height: 1080 });
  
  // Log console messages
  page.on('console', msg => console.log('Browser console:', msg.text()));
  page.on('error', err => console.error('Browser error:', err));
  page.on('pageerror', err => console.error('Page error:', err));
  
  try {
    // First, go to dashboard
    console.log('Navigating to dashboard...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Take screenshot of dashboard
    await page.screenshot({ 
      path: 'dashboard-screenshot.png',
      fullPage: true 
    });
    console.log('Dashboard screenshot saved');
    
    // Navigate directly to a podcast view
    console.log('Navigating to podcast view...');
    try {
      await page.goto('http://localhost:3000/podcast/MFM', { 
        waitUntil: 'domcontentloaded',
        timeout: 10000 
      });
    } catch (navError) {
      console.log('Navigation error (continuing anyway):', navError.message);
    }
    
    // Give graph time to render
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Take screenshot of podcast view
    await page.screenshot({ 
      path: 'podcast-view-screenshot.png',
      fullPage: true 
    });
    console.log('Podcast view screenshot saved');
    
    // Get page dimensions and content info
    const dimensions = await page.evaluate(() => {
      const graphContainer = document.querySelector('canvas');
      return {
        pageWidth: window.innerWidth,
        pageHeight: window.innerHeight,
        bodyWidth: document.body.clientWidth,
        bodyHeight: document.body.clientHeight,
        hasCanvas: !!graphContainer,
        canvasInfo: graphContainer ? {
          width: graphContainer.width,
          height: graphContainer.height,
          style: {
            width: graphContainer.style.width,
            height: graphContainer.style.height
          }
        } : null,
        containerInfo: document.querySelector('.container') ? {
          width: document.querySelector('.container').clientWidth,
          height: document.querySelector('.container').clientHeight,
          styles: window.getComputedStyle(document.querySelector('.container'))
        } : null
      };
    });
    
    console.log('Page dimensions:', JSON.stringify(dimensions, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
})();