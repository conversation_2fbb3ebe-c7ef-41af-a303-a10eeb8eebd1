import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import ForceGraph3D from 'react-force-graph-3d'

function GraphVisualization({ podcastId }) {
  const navigate = useNavigate()
  const [graphData, setGraphData] = useState({ nodes: [], links: [] })
  const [viewState, setViewState] = useState({
    loading: true,
    mode: 'clusters',
    selectedNode: null,
    clusterId: null,
    clusterLabel: null
  })
  const [filters, setFilters] = useState({
    contentTypes: [],
    themes: [],
    speakers: [],
    showOnlyImportant: false
  })
  const [availableFilters, setAvailableFilters] = useState({
    contentTypes: {},
    themes: {},
    speakers: []
  })
  const fgRef = useRef()
  const autoFitTimeoutRef = useRef(null)
  const isAutoFittingRef = useRef(false)
  const engineStoppedRef = useRef(false)

  // Semantic color mapping for different content types
  const getSemanticColor = (node) => {
    if (viewState.mode === 'clusters') {
      // Use auto-coloring for clusters
      return null
    }

    // Color meaningful units by content type
    const contentTypeColors = {
      'insight': '#FFD700',    // Gold - insights are valuable
      'quote': '#FF6B6B',      // Coral - quotes stand out
      'advice': '#4ECDC4',     // Teal - actionable advice
      'story': '#45B7D1',      // Sky blue - stories are engaging
      'concept': '#96CEB4',    // Mint - concepts are foundational
      'default': '#FFFFFF'     // White fallback
    }

    return contentTypeColors[node.content_type] || contentTypeColors.default
  }

  // Calculate node size based on importance and type
  const getNodeSize = (node) => {
    if (viewState.mode === 'clusters') {
      // Logarithmic scaling for cluster size based on member count
      const memberCount = node.member_count || 1
      return Math.max(6, Math.min(25, 8 + Math.log(memberCount) * 1.5))
    } else {
      // Size meaningful units based on importance
      const baseSize = 4
      const importanceMultiplier = (node.importance_score || 0.5) * 3
      return Math.max(3, Math.min(8, baseSize + importanceMultiplier))
    }
  }

  // Filter nodes based on current filter settings
  const getFilteredData = () => {
    if (viewState.mode === 'clusters' || !graphData.nodes.length) {
      return graphData
    }

    let filteredNodes = graphData.nodes

    // Filter by content types
    if (filters.contentTypes.length > 0) {
      filteredNodes = filteredNodes.filter(node =>
        filters.contentTypes.includes(node.content_type)
      )
    }

    // Filter by themes
    if (filters.themes.length > 0) {
      filteredNodes = filteredNodes.filter(node =>
        node.themes && node.themes.some(theme => filters.themes.includes(theme))
      )
    }

    // Filter by speakers
    if (filters.speakers.length > 0) {
      filteredNodes = filteredNodes.filter(node =>
        filters.speakers.includes(node.primary_speaker)
      )
    }

    // Filter by importance
    if (filters.showOnlyImportant) {
      filteredNodes = filteredNodes.filter(node =>
        (node.importance_score || 0) > 0.7
      )
    }

    // Filter links to only include those between visible nodes
    const visibleNodeIds = new Set(filteredNodes.map(n => n.id))
    const filteredLinks = graphData.links.filter(link =>
      visibleNodeIds.has(link.source) && visibleNodeIds.has(link.target)
    )

    return {
      nodes: filteredNodes,
      links: filteredLinks
    }
  }

  // Simple performance optimization for large graphs
  const nodeCount = graphData.nodes.length
  const isLargeGraph = nodeCount > 500
  const nodeResolution = isLargeGraph ? 8 : 16
  const enableAntialiasing = !isLargeGraph

  // Debounced auto-fit function to prevent oscillation
  const debouncedAutoFit = useCallback((duration = 400, padding = 40, delay = 0, requireEngineStop = false) => {
    // Clear any pending auto-fit
    if (autoFitTimeoutRef.current) {
      clearTimeout(autoFitTimeoutRef.current)
    }

    // Don't start new auto-fit if one is already in progress
    if (isAutoFittingRef.current) {
      return
    }

    // If requireEngineStop is true, only proceed if engine has stopped
    if (requireEngineStop && !engineStoppedRef.current) {
      return
    }

    autoFitTimeoutRef.current = setTimeout(() => {
      if (fgRef.current && !isAutoFittingRef.current) {
        isAutoFittingRef.current = true
        fgRef.current.zoomToFit(duration, padding)

        // Reset the flag after animation completes
        setTimeout(() => {
          isAutoFittingRef.current = false
        }, duration + 50) // Add small buffer
      }
    }, delay)
  }, [])

  useEffect(() => {
    fetchGraphData()
  }, [podcastId, viewState.mode, viewState.clusterId])

  // Handle window resize to maintain proper aspect ratio
  useEffect(() => {
    const handleResize = () => {
      if (fgRef.current) {
        fgRef.current.width(window.innerWidth)
        fgRef.current.height(window.innerHeight)
        // Only auto-fit after engine has stopped to prevent conflicts during simulation
        debouncedAutoFit(300, 40, 100, true)
      }
    }

    const handleVisibilityChange = () => {
      if (!document.hidden && fgRef.current) {
        // Only auto-fit after engine has stopped to prevent conflicts during simulation
        debouncedAutoFit(400, 40, 200, true)
      }
    }

    window.addEventListener('resize', handleResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('resize', handleResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      // Clean up timeout on unmount
      if (autoFitTimeoutRef.current) {
        clearTimeout(autoFitTimeoutRef.current)
      }
    }
  }, [debouncedAutoFit])

  // Initialize graph settings for optimal rendering
  useEffect(() => {
    if (fgRef.current) {
      const fg = fgRef.current

      // Ensure proper aspect ratio and rendering
      fg.renderer().setPixelRatio(window.devicePixelRatio || 1)

      // Optimize for smooth sphere rendering
      if (enableAntialiasing) {
        fg.renderer().antialias = true
      }
    }
  }, [graphData, enableAntialiasing])

  const fetchGraphData = async () => {
    try {
      // Use the new consolidated /graph endpoint
      let url = `/api/v1/podcasts/${podcastId}/graph?view=${viewState.mode}`
      if (viewState.mode === 'units' && viewState.clusterId) {
        url += `&cluster_id=${viewState.clusterId}`
      }
      
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch graph data')
      const data = await response.json()
      
      setGraphData(data)
      // Reset engine stopped flag when new data loads
      engineStoppedRef.current = false
      if (data.cluster_label) {
        setViewState(prev => ({ ...prev, clusterLabel: data.cluster_label }))
      }

      // Update available filters for units view
      if (viewState.mode === 'units' && data.stats) {
        setAvailableFilters({
          contentTypes: data.stats.content_types || {},
          themes: data.stats.themes || {},
          speakers: [...new Set(data.nodes.map(n => n.primary_speaker).filter(Boolean))]
        })
      }
    } catch (err) {
      console.error('Error fetching graph data:', err)
    } finally {
      setViewState(prev => ({ ...prev, loading: false }))
    }
  }

  const handleNodeClick = (node) => {
    if (viewState.mode === 'clusters') {
      // In clusters view, clicking a cluster drills down to units
      setViewState(prev => ({
        ...prev,
        clusterId: node.id,
        mode: 'units',
        selectedNode: null,
        loading: true
      }))
    } else {
      // In units view, clicking shows node details
      setViewState(prev => ({ ...prev, selectedNode: node }))
    }
  }

  const handleBackClick = () => {
    if (viewState.mode === 'units') {
      // Go back to clusters view
      setViewState({
        loading: true,
        mode: 'clusters',
        selectedNode: null,
        clusterId: null,
        clusterLabel: null
      })
    } else {
      // Go back to dashboard
      navigate('/')
    }
  }

  const filteredData = getFilteredData()

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <ForceGraph3D
        ref={fgRef}
        graphData={filteredData}
        nodeLabel={node => {
          if (viewState.mode === 'clusters') {
            return `${node.name} (${node.member_count} units)`
          } else {
            return `${node.name}\nType: ${node.content_type}\nImportance: ${Math.round((node.importance_score || 0) * 100)}%`
          }
        }}
        nodeAutoColorBy={viewState.mode === 'clusters' ? 'id' : null}
        nodeColor={viewState.mode === 'units' ? getSemanticColor : undefined}
        onNodeClick={handleNodeClick}
        linkOpacity={link => {
          // Vary opacity based on relationship type
          if (link.relationship === 'sequential') return 0.9
          if (link.relationship?.startsWith('theme:')) return 0.6
          if (link.relationship?.startsWith('speaker:')) return 0.4
          return 0.8
        }}
        linkColor={link => link.color || '#ffffff'}
        linkWidth={link => (link.width || 4) * 0.75}
        nodeOpacity={0.9}
        backgroundColor="#000011"
        width={window.innerWidth}
        height={window.innerHeight}
        nodeVal={getNodeSize}
        nodeResolution={nodeResolution}
        enableNodeDrag={false}
        enableNavigationControls={true}
        showNavInfo={false}
        cooldownTime={1500}  // Stop simulation after 1.5 seconds for smoother UX
        warmupTicks={50}  // Pre-calculate physics for smoother initial render
        // Force simulation parameters to prevent oscillation
        d3AlphaDecay={0.02}  // Slower alpha decay for smoother settling (default: 0.0228)
        d3VelocityDecay={0.3}  // Higher velocity decay to reduce bouncing (default: 0.4)
        d3AlphaMin={0.001}  // Lower alpha minimum for more stable end state (default: 0.001)
        // Link force parameters
        linkStrength={0.1}  // Weaker link forces to reduce oscillation (default: 1)
        // Charge force parameters
        chargeStrength={-30}  // Moderate repulsion to prevent clustering but reduce bounce
        chargeDistanceMax={200}  // Limit charge distance for better performance
        onEngineStop={() => {
          // Mark engine as stopped
          engineStoppedRef.current = true
          // Use debounced auto-fit when physics simulation settles
          // This is the primary auto-fit that should happen after initial load
          debouncedAutoFit(400, 40, 100) // Small delay to ensure engine is fully stopped
        }}

      />
      <button
        onClick={handleBackClick}
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          background: 'rgba(0, 0, 0, 0.5)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: '#fff',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          zIndex: 1000
        }}
      >
        ← {viewState.mode === 'units' ? 'Back to Clusters' : 'Back to Dashboard'}
      </button>

      {/* Breadcrumb Navigation */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        background: 'rgba(0, 0, 0, 0.8)',
        padding: '8px 16px',
        borderRadius: '6px',
        color: '#fff',
        fontSize: '14px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <span style={{ color: '#aaa' }}>Podcast Knowledge</span>
        <span style={{ color: '#666' }}>→</span>
        <span style={{ color: viewState.mode === 'clusters' ? '#fff' : '#aaa' }}>
          Clusters
        </span>
        {viewState.mode === 'units' && viewState.clusterLabel && (
          <>
            <span style={{ color: '#666' }}>→</span>
            <span style={{ color: '#fff', fontWeight: 'bold' }}>
              {viewState.clusterLabel}
            </span>
          </>
        )}
      </div>

      {/* Visual Legend for Units View */}
      {viewState.mode === 'units' && !viewState.loading && (
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px',
          background: 'rgba(0, 0, 0, 0.9)',
          padding: '12px',
          borderRadius: '6px',
          color: '#fff',
          fontSize: '12px',
          minWidth: '200px'
        }}>
          <h5 style={{ margin: '0 0 8px 0', fontSize: '13px' }}>Legend</h5>

          <div style={{ marginBottom: '8px' }}>
            <strong>Content Types:</strong>
          </div>

          {[
            { type: 'insight', color: '#FFD700', label: 'Insights' },
            { type: 'quote', color: '#FF6B6B', label: 'Quotes' },
            { type: 'advice', color: '#4ECDC4', label: 'Advice' },
            { type: 'story', color: '#45B7D1', label: 'Stories' },
            { type: 'concept', color: '#96CEB4', label: 'Concepts' }
          ].map(({ type, color, label }) => (
            <div key={type} style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              marginBottom: '3px'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                backgroundColor: color
              }}></div>
              <span>{label}</span>
            </div>
          ))}

          <div style={{ marginTop: '8px', fontSize: '11px', color: '#aaa' }}>
            Size = Importance • Lines = Relationships
          </div>
        </div>
      )}

      {/* Filtering Controls for Units View */}
      {viewState.mode === 'units' && !viewState.loading && (
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          background: 'rgba(0, 0, 0, 0.9)',
          padding: '15px',
          borderRadius: '8px',
          color: '#fff',
          minWidth: '250px',
          maxWidth: '350px',
          zIndex: 1000,
          fontSize: '14px'
        }}>
          <h4 style={{ margin: '0 0 15px 0', fontSize: '16px' }}>Filter Units</h4>

          {/* Content Type Filter */}
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Content Type:
            </label>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
              {Object.entries(availableFilters.contentTypes).map(([type, count]) => (
                <button
                  key={type}
                  onClick={() => {
                    setFilters(prev => ({
                      ...prev,
                      contentTypes: prev.contentTypes.includes(type)
                        ? prev.contentTypes.filter(t => t !== type)
                        : [...prev.contentTypes, type]
                    }))
                  }}
                  style={{
                    padding: '4px 8px',
                    fontSize: '12px',
                    border: '1px solid #555',
                    borderRadius: '4px',
                    background: filters.contentTypes.includes(type) ? '#4A90E2' : 'transparent',
                    color: '#fff',
                    cursor: 'pointer'
                  }}
                >
                  {type} ({count})
                </button>
              ))}
            </div>
          </div>

          {/* Theme Filter */}
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Themes:
            </label>
            <select
              multiple
              value={filters.themes}
              onChange={(e) => {
                const selectedThemes = Array.from(e.target.selectedOptions, option => option.value)
                setFilters(prev => ({ ...prev, themes: selectedThemes }))
              }}
              style={{
                width: '100%',
                height: '60px',
                background: '#2a2a2a',
                color: '#fff',
                border: '1px solid #555',
                borderRadius: '4px',
                fontSize: '12px'
              }}
            >
              {Object.entries(availableFilters.themes).slice(0, 10).map(([theme, count]) => (
                <option key={theme} value={theme}>
                  {theme} ({count})
                </option>
              ))}
            </select>
          </div>

          {/* Importance Filter */}
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <input
                type="checkbox"
                checked={filters.showOnlyImportant}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  showOnlyImportant: e.target.checked
                }))}
              />
              <span>Show only important units</span>
            </label>
          </div>

          {/* Clear Filters */}
          <button
            onClick={() => setFilters({
              contentTypes: [],
              themes: [],
              speakers: [],
              showOnlyImportant: false
            })}
            style={{
              width: '100%',
              padding: '6px',
              background: '#666',
              color: '#fff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            Clear All Filters
          </button>

          {/* Stats */}
          <div style={{
            marginTop: '10px',
            fontSize: '11px',
            color: '#aaa',
            borderTop: '1px solid #555',
            paddingTop: '8px'
          }}>
            Showing {filteredData.nodes.length} of {graphData.nodes.length} units
          </div>
        </div>
      )}

      {/* Selected Node Details */}
      {viewState.selectedNode && viewState.mode === 'units' && (
        <div style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          background: 'rgba(0, 0, 0, 0.9)',
          padding: '15px',
          borderRadius: '8px',
          color: '#fff',
          maxWidth: '350px',
          zIndex: 1000
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: getSemanticColor(viewState.selectedNode) }}>
            {viewState.selectedNode.content_type?.toUpperCase() || 'UNIT'}
          </h4>
          <p style={{ margin: '0 0 8px 0', fontSize: '14px' }}>
            {viewState.selectedNode.name}
          </p>

          <div style={{ fontSize: '12px', color: '#ccc' }}>
            {viewState.selectedNode.primary_speaker && (
              <div>Speaker: {viewState.selectedNode.primary_speaker}</div>
            )}
            {viewState.selectedNode.importance_score && (
              <div>Importance: {Math.round(viewState.selectedNode.importance_score * 100)}%</div>
            )}
            {viewState.selectedNode.themes && viewState.selectedNode.themes.length > 0 && (
              <div style={{ marginTop: '8px' }}>
                <strong>Themes:</strong>
                <div style={{ marginTop: '4px' }}>
                  {viewState.selectedNode.themes.slice(0, 3).map(theme => (
                    <span
                      key={theme}
                      style={{
                        display: 'inline-block',
                        background: '#444',
                        padding: '2px 6px',
                        borderRadius: '3px',
                        marginRight: '4px',
                        marginBottom: '2px',
                        fontSize: '11px'
                      }}
                    >
                      {theme}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default GraphVisualization