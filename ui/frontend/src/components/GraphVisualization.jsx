import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import ForceGraph3D from 'react-force-graph-3d'

function GraphVisualization({ podcastId }) {
  const navigate = useNavigate()
  const [graphData, setGraphData] = useState({ nodes: [], links: [] })
  const [viewState, setViewState] = useState({
    loading: true,
    mode: 'clusters',
    selectedNode: null,
    clusterId: null,
    clusterLabel: null
  })
  const fgRef = useRef()
  const autoFitTimeoutRef = useRef(null)
  const isAutoFittingRef = useRef(false)
  const engineStoppedRef = useRef(false)

  // Simple performance optimization for large graphs
  const nodeCount = graphData.nodes.length
  const isLargeGraph = nodeCount > 500
  const nodeResolution = isLargeGraph ? 8 : 16
  const enableAntialiasing = !isLargeGraph

  // Debounced auto-fit function to prevent oscillation
  const debouncedAutoFit = useCallback((duration = 400, padding = 40, delay = 0, requireEngineStop = false) => {
    // Clear any pending auto-fit
    if (autoFitTimeoutRef.current) {
      clearTimeout(autoFitTimeoutRef.current)
    }

    // Don't start new auto-fit if one is already in progress
    if (isAutoFittingRef.current) {
      return
    }

    // If requireEngineStop is true, only proceed if engine has stopped
    if (requireEngineStop && !engineStoppedRef.current) {
      return
    }

    autoFitTimeoutRef.current = setTimeout(() => {
      if (fgRef.current && !isAutoFittingRef.current) {
        isAutoFittingRef.current = true
        fgRef.current.zoomToFit(duration, padding)

        // Reset the flag after animation completes
        setTimeout(() => {
          isAutoFittingRef.current = false
        }, duration + 50) // Add small buffer
      }
    }, delay)
  }, [])

  useEffect(() => {
    fetchGraphData()
  }, [podcastId, viewState.mode, viewState.clusterId])

  // Handle window resize to maintain proper aspect ratio
  useEffect(() => {
    const handleResize = () => {
      if (fgRef.current) {
        fgRef.current.width(window.innerWidth)
        fgRef.current.height(window.innerHeight)
        // Only auto-fit after engine has stopped to prevent conflicts during simulation
        debouncedAutoFit(300, 40, 100, true)
      }
    }

    const handleVisibilityChange = () => {
      if (!document.hidden && fgRef.current) {
        // Only auto-fit after engine has stopped to prevent conflicts during simulation
        debouncedAutoFit(400, 40, 200, true)
      }
    }

    window.addEventListener('resize', handleResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('resize', handleResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      // Clean up timeout on unmount
      if (autoFitTimeoutRef.current) {
        clearTimeout(autoFitTimeoutRef.current)
      }
    }
  }, [debouncedAutoFit])

  // Initialize graph settings for optimal rendering
  useEffect(() => {
    if (fgRef.current) {
      const fg = fgRef.current

      // Ensure proper aspect ratio and rendering
      fg.renderer().setPixelRatio(window.devicePixelRatio || 1)

      // Optimize for smooth sphere rendering
      if (enableAntialiasing) {
        fg.renderer().antialias = true
      }
    }
  }, [graphData, enableAntialiasing])

  const fetchGraphData = async () => {
    try {
      // Use the new consolidated /graph endpoint
      let url = `/api/v1/podcasts/${podcastId}/graph?view=${viewState.mode}`
      if (viewState.mode === 'units' && viewState.clusterId) {
        url += `&cluster_id=${viewState.clusterId}`
      }
      
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch graph data')
      const data = await response.json()
      
      setGraphData(data)
      // Reset engine stopped flag when new data loads
      engineStoppedRef.current = false
      if (data.cluster_label) {
        setViewState(prev => ({ ...prev, clusterLabel: data.cluster_label }))
      }
    } catch (err) {
      console.error('Error fetching graph data:', err)
    } finally {
      setViewState(prev => ({ ...prev, loading: false }))
    }
  }

  const handleNodeClick = (node) => {
    if (viewState.mode === 'clusters') {
      // In clusters view, clicking a cluster drills down to units
      setViewState(prev => ({
        ...prev,
        clusterId: node.id,
        mode: 'units',
        selectedNode: null,
        loading: true
      }))
    } else {
      // In units view, clicking shows node details
      setViewState(prev => ({ ...prev, selectedNode: node }))
    }
  }

  const handleBackClick = () => {
    if (viewState.mode === 'units') {
      // Go back to clusters view
      setViewState({
        loading: true,
        mode: 'clusters',
        selectedNode: null,
        clusterId: null,
        clusterLabel: null
      })
    } else {
      // Go back to dashboard
      navigate('/')
    }
  }

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <ForceGraph3D
        ref={fgRef}
        graphData={graphData}
        nodeLabel="name"
        nodeAutoColorBy={viewState.mode === 'clusters' ? 'id' : 'cluster'}
        onNodeClick={handleNodeClick}
        linkOpacity={0.8}
        linkColor={link => link.color || '#ffffff'}
        linkWidth={link => (link.width || 4) * 0.75}
        nodeOpacity={0.9}
        backgroundColor="#000011"
        width={window.innerWidth}
        height={window.innerHeight}
        nodeVal={node => {
          if (viewState.mode === 'clusters') {
            // Logarithmic scaling for cluster size based on member count
            const memberCount = node.member_count || 1
            return Math.max(6, Math.min(25, 8 + Math.log(memberCount) * 1.5))
          } else {
            // Uniform size for meaningful units
            return 5
          }
        }}
        nodeResolution={nodeResolution}
        enableNodeDrag={false}
        enableNavigationControls={true}
        showNavInfo={false}
        cooldownTime={1500}  // Stop simulation after 1.5 seconds for smoother UX
        warmupTicks={50}  // Pre-calculate physics for smoother initial render
        // Force simulation parameters to prevent oscillation
        d3AlphaDecay={0.02}  // Slower alpha decay for smoother settling (default: 0.0228)
        d3VelocityDecay={0.3}  // Higher velocity decay to reduce bouncing (default: 0.4)
        d3AlphaMin={0.001}  // Lower alpha minimum for more stable end state (default: 0.001)
        // Link force parameters
        linkStrength={0.1}  // Weaker link forces to reduce oscillation (default: 1)
        // Charge force parameters
        chargeStrength={-30}  // Moderate repulsion to prevent clustering but reduce bounce
        chargeDistanceMax={200}  // Limit charge distance for better performance
        onEngineStop={() => {
          // Mark engine as stopped
          engineStoppedRef.current = true
          // Use debounced auto-fit when physics simulation settles
          // This is the primary auto-fit that should happen after initial load
          debouncedAutoFit(400, 40, 100) // Small delay to ensure engine is fully stopped
        }}

      />
      <button
        onClick={handleBackClick}
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          background: 'rgba(0, 0, 0, 0.5)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: '#fff',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          zIndex: 1000
        }}
      >
        ← {viewState.mode === 'units' ? 'Back to Clusters' : 'Back to Dashboard'}
      </button>
      {viewState.mode === 'units' && viewState.clusterLabel && (
        <div style={{ 
          position: 'absolute', 
          top: '20px', 
          left: '50%', 
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.5)',
          padding: '10px 20px',
          borderRadius: '5px',
          color: '#fff',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          {viewState.clusterLabel}
        </div>
      )}
      {viewState.selectedNode && viewState.mode === 'units' && (
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          background: 'rgba(0, 0, 0, 0.8)',
          padding: '10px',
          borderRadius: '5px',
          color: '#fff',
          maxWidth: '300px',
          zIndex: 1000
        }}>
          <h4 style={{ margin: '0 0 10px 0' }}>Selected Unit</h4>
          <p style={{ margin: 0 }}>{viewState.selectedNode.name}</p>
          {viewState.selectedNode.themes && (
            <p style={{ margin: '5px 0 0 0', fontSize: '0.9em', color: '#a0a0a0' }}>
              Themes: {viewState.selectedNode.themes}
            </p>
          )}
        </div>
      )}
    </div>
  )
}

export default GraphVisualization