* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #1e1e1e;
  color: #e0e0e0;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.podcast-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  display: block;
}

.podcast-card:hover {
  background-color: #333333;
  transform: translateY(-2px);
}

.podcast-card h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.podcast-card .description {
  font-size: 0.875rem;
  color: #c0c0c0;
  margin-bottom: 1rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.podcast-card .metadata {
  font-size: 0.875rem;
  color: #a0a0a0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.podcast-card .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4caf50;
  display: inline-block;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
}

.sort-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.sort-controls select {
  background-color: #2a2a2a;
  color: #e0e0e0;
  border: 1px solid #444;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
}

.loading {
  text-align: center;
  padding: 4rem;
  color: #a0a0a0;
}

.error {
  text-align: center;
  padding: 4rem;
  color: #f44336;
}