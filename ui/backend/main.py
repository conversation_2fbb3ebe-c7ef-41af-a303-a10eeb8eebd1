from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
from neo4j import GraphDatabase
import os
import sys
from pathlib import Path
import yaml
from api.routes import meaningful_units

# Add parent directory to path to import from seeding_pipeline
sys.path.append(str(Path(__file__).parent.parent.parent))

app = FastAPI()

# Enable CORS for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register routers
app.include_router(meaningful_units.router, prefix="/api/v1")

# Simple database connection (reusing from meaningful_units)
def get_db_driver():
    """Simple database connection using environment variables."""
    uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    password = os.environ.get('NEO4J_PASSWORD', 'password')
    return GraphDatabase.driver(uri, auth=("neo4j", password))

@app.get("/api/v1/podcasts")
async def get_podcasts():
    """Get list of podcasts from configuration file and their stats from databases."""
    try:
        # Load podcast configuration
        config_path = Path(__file__).parent.parent.parent / "seeding_pipeline" / "config" / "podcasts.yaml"
        
        if not config_path.exists():
            return {"error": "Podcast configuration not found", "podcasts": []}
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        podcasts_list = []
        
        # Process each podcast from configuration
        for podcast_config in config.get('podcasts', []):
            if not podcast_config.get('enabled', True):
                continue
            
            podcast_info = {
                'id': podcast_config['id'],
                'name': podcast_config['name'],
                'description': podcast_config.get('metadata', {}).get('description', ''),
                'host': podcast_config.get('metadata', {}).get('host', ''),
                'category': podcast_config.get('metadata', {}).get('category', ''),
                'tags': podcast_config.get('metadata', {}).get('tags', []),
                'website': podcast_config.get('metadata', {}).get('website', ''),
                'episode_count': 0,
                'last_updated': None
            }
            
            # Try to get stats from the podcast's database
            try:
                db_config = podcast_config.get('database', {})
                uri = db_config.get('uri', 'bolt://localhost:7687')
                password = db_config.get('neo4j_password', 'password')
                
                driver = GraphDatabase.driver(uri, auth=("neo4j", password))
                
                with driver.session() as session:
                    # Get episode count and last update
                    result = session.run("""
                        MATCH (e:Episode)
                        RETURN count(e) as episode_count,
                               max(e.published_date) as last_updated
                    """)
                    record = result.single()
                    
                    if record:
                        podcast_info['episode_count'] = record['episode_count'] or 0
                        podcast_info['last_updated'] = record['last_updated'] or datetime.now().isoformat()
                
                driver.close()
            except Exception as db_error:
                # If we can't connect to the database, just use the config info
                print(f"Could not get stats for {podcast_config['name']}: {db_error}")
                podcast_info['last_updated'] = datetime.now().isoformat()
            
            podcasts_list.append(podcast_info)
        
        return {"podcasts": podcasts_list}
    
    except Exception as e:
        print(f"Error loading podcasts: {e}")
        return {"error": str(e), "podcasts": []}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)