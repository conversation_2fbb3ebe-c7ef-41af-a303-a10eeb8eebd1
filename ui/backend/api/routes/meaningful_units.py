from fastapi import APIRouter, HTTPException, Query
from neo4j import GraphDatabase
import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional

router = APIRouter()

# Cache for podcast configurations
_podcast_configs = None

def load_podcast_configs():
    """Load podcast configurations from YAML file."""
    global _podcast_configs
    if _podcast_configs is None:
        config_path = Path(__file__).parent.parent.parent.parent.parent / "seeding_pipeline" / "config" / "podcasts.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        _podcast_configs = {p['id']: p for p in config.get('podcasts', [])}
    return _podcast_configs

# Database connection per podcast
def get_db_driver(podcast_id: str):
    """Get database connection for specific podcast."""
    configs = load_podcast_configs()
    
    if podcast_id not in configs:
        # Fallback to default if podcast not found
        uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
        password = os.environ.get('NEO4J_PASSWORD', 'password')
        return GraphDatabase.driver(uri, auth=("neo4j", password))
    
    podcast_config = configs[podcast_id]
    db_config = podcast_config.get('database', {})
    uri = db_config.get('uri', 'bolt://localhost:7687')
    password = db_config.get('neo4j_password', 'password')
    
    return GraphDatabase.driver(uri, auth=("neo4j", password))

@router.get("/podcasts/{podcast_id}/graph")
async def get_podcast_graph(
    podcast_id: str,
    view: str = Query(default="clusters", description="View type: 'clusters' or 'units'"),
    cluster_id: Optional[str] = Query(default=None, description="Cluster ID for units view")
) -> Dict[str, Any]:
    """Single flexible endpoint for all graph data."""
    
    driver = get_db_driver(podcast_id)
    
    try:
        with driver.session() as session:
            if view == "clusters":
                return _get_clusters_data(session)
            else:
                if not cluster_id:
                    raise HTTPException(status_code=400, detail="cluster_id required for units view")
                return _get_units_data(session, cluster_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        driver.close()

def _get_clusters_data(session):
    """Get clusters view data."""
    # Simple clusters query
    clusters_query = """
    MATCH (c:Cluster)
    WHERE c.status = 'active' 
      AND NOT toLower(c.label) CONTAINS 'outro'
      AND NOT toLower(c.label) CONTAINS 'intro'
      AND NOT toLower(c.label) CONTAINS 'sponsor'
      AND NOT toLower(c.label) CONTAINS 'ad'
      AND NOT toLower(c.label) CONTAINS 'commercial'
      AND NOT toLower(c.label) CONTAINS 'opening'
      AND NOT toLower(c.label) CONTAINS 'closing'
      AND NOT toLower(c.label) CONTAINS 'promotion'
    RETURN c.id as id, c.label as label, c.member_count as member_count
    ORDER BY c.member_count DESC
    """
    
    clusters_result = session.run(clusters_query)
    cluster_records = list(clusters_result)
    
    # Build nodes
    nodes = []
    for record in cluster_records:
        nodes.append({
            "id": record["id"],
            "name": record["label"],
            "label": record["label"],
            "member_count": record["member_count"]
        })
    
    # Find cluster connections through shared entities
    links_query = """
    MATCH (c1:Cluster)<-[:IN_CLUSTER]-(m1:MeaningfulUnit)-[:PART_OF]->(ep:Episode)<-[:MENTIONED_IN]-(e:Entity)
    MATCH (e)-[:MENTIONED_IN]->(ep2:Episode)<-[:PART_OF]-(m2:MeaningfulUnit)-[:IN_CLUSTER]->(c2:Cluster)
    WHERE c1.id < c2.id
      AND c1.status = 'active' 
      AND c2.status = 'active'
      AND NOT toLower(c1.label) CONTAINS 'outro'
      AND NOT toLower(c1.label) CONTAINS 'intro'
      AND NOT toLower(c1.label) CONTAINS 'sponsor'
      AND NOT toLower(c1.label) CONTAINS 'ad'
      AND NOT toLower(c1.label) CONTAINS 'commercial'
      AND NOT toLower(c1.label) CONTAINS 'opening'
      AND NOT toLower(c1.label) CONTAINS 'closing'
      AND NOT toLower(c1.label) CONTAINS 'promotion'
      AND NOT toLower(c2.label) CONTAINS 'outro'
      AND NOT toLower(c2.label) CONTAINS 'intro'
      AND NOT toLower(c2.label) CONTAINS 'sponsor'
      AND NOT toLower(c2.label) CONTAINS 'ad'
      AND NOT toLower(c2.label) CONTAINS 'commercial'
      AND NOT toLower(c2.label) CONTAINS 'opening'
      AND NOT toLower(c2.label) CONTAINS 'closing'
      AND NOT toLower(c2.label) CONTAINS 'promotion'
    RETURN DISTINCT c1.id as source, c2.id as target
    """
    
    links_result = session.run(links_query)
    links = []
    for record in links_result:
        links.append({
            "source": record["source"],
            "target": record["target"],
            "color": "#ffffff",
            "width": 3
        })
    
    return {"nodes": nodes, "links": links}

def _get_units_data(session, cluster_id: str):
    """Get units view data for a specific cluster with intelligent linking."""
    # Enhanced query to get more unit properties for intelligent linking
    query = """
    MATCH (c:Cluster {id: $cluster_id})
    MATCH (m:MeaningfulUnit)-[:IN_CLUSTER]->(c)
    WITH m, c
    ORDER BY m.start_time
    WITH c, collect({
        id: m.id,
        summary: m.summary,
        themes: m.themes,
        start_time: m.start_time,
        end_time: m.end_time,
        unit_type: m.unit_type,
        primary_speaker: m.primary_speaker
    }) as units
    RETURN c.label as cluster_label, units
    """

    result = session.run(query, cluster_id=cluster_id)
    record = result.single()

    if not record:
        raise HTTPException(status_code=404, detail=f"Cluster {cluster_id} not found")

    units_data = record["units"]
    cluster_label = record["cluster_label"]

    # Build enhanced nodes with semantic properties
    nodes = []
    for unit in units_data:
        # Determine content type from themes and unit_type
        content_type = _determine_content_type(unit)
        importance_score = _calculate_importance_score(unit)

        nodes.append({
            "id": unit["id"],
            "name": _smart_truncate_summary(unit["summary"]),
            "cluster": cluster_id,
            "themes": unit["themes"],
            "content_type": content_type,
            "importance_score": importance_score,
            "start_time": unit["start_time"],
            "end_time": unit["end_time"],
            "primary_speaker": unit["primary_speaker"],
            "unit_type": unit.get("unit_type", "discussion")
        })

    # Create intelligent links instead of full mesh
    links = _create_intelligent_links(units_data)

    return {
        "nodes": nodes,
        "links": links,
        "cluster_label": cluster_label,
        "stats": {
            "total_units": len(units_data),
            "content_types": _get_content_type_distribution(nodes),
            "themes": _get_theme_distribution(units_data)
        }
    }


def _determine_content_type(unit):
    """Determine content type based on themes and properties."""
    themes = unit.get("themes", [])
    unit_type = unit.get("unit_type", "")
    summary = unit.get("summary", "").lower() if unit.get("summary") else ""

    # Check for quotes (look for quotation patterns)
    if ('"' in summary or "said" in summary or "quote" in summary or
        "mentioned" in summary or "stated" in summary):
        return "quote"

    # Check for insights (look for insight keywords)
    insight_keywords = ["insight", "key point", "takeaway", "important", "realize",
                       "learned", "discovery", "understanding", "breakthrough"]
    if any(keyword in summary for keyword in insight_keywords):
        return "insight"

    # Check for advice/actionable content
    advice_keywords = ["should", "recommend", "suggest", "tip", "strategy", "how to",
                      "advice", "guidance", "steps", "method", "approach", "technique"]
    if any(keyword in summary for keyword in advice_keywords):
        return "advice"

    # Check for stories/examples
    story_keywords = ["story", "example", "experience", "happened", "time when",
                     "remember", "once", "anecdote", "case", "situation"]
    if any(keyword in summary for keyword in story_keywords):
        return "story"

    # Check for concepts/explanations
    concept_keywords = ["concept", "definition", "explain", "theory", "principle",
                       "framework", "model", "system", "process"]
    if any(keyword in summary for keyword in concept_keywords):
        return "concept"

    # Default to discussion for general content
    return "discussion"


def _calculate_importance_score(unit):
    """Calculate importance score based on unit properties."""
    score = 0.5  # Base score

    summary = unit.get("summary", "")
    themes = unit.get("themes", [])

    # Longer summaries might be more important
    if len(summary) > 200:
        score += 0.2

    # More themes suggest richer content
    if len(themes) > 2:
        score += 0.2

    # Key phrases that suggest importance
    important_phrases = ["key", "important", "crucial", "main", "primary", "essential"]
    if any(phrase in summary.lower() for phrase in important_phrases):
        score += 0.3

    return min(1.0, score)


def _smart_truncate_summary(summary):
    """Intelligently truncate summary at sentence boundaries."""
    if not summary:
        return "No summary"

    if len(summary) <= 80:
        return summary

    # Try to truncate at sentence boundary
    sentences = summary.split('. ')
    if len(sentences) > 1 and len(sentences[0]) <= 80:
        return sentences[0] + '.'

    # Fallback to word boundary
    words = summary.split()
    truncated = []
    char_count = 0

    for word in words:
        if char_count + len(word) + 1 > 80:
            break
        truncated.append(word)
        char_count += len(word) + 1

    return ' '.join(truncated) + '...'


def _create_intelligent_links(units_data):
    """Create semantic-based links instead of full mesh."""
    links = []

    if len(units_data) <= 1:
        return links

    # Strategy 1: Sequential connections (temporal flow)
    for i in range(len(units_data) - 1):
        links.append({
            "source": units_data[i]["id"],
            "target": units_data[i + 1]["id"],
            "color": "#ffffff",
            "width": 1.0,
            "relationship": "sequential"
        })

    # Strategy 2: Theme-based connections (max 2 per theme to avoid clutter)
    theme_connections = _create_theme_based_links(units_data)
    links.extend(theme_connections)

    # Strategy 3: Speaker-based connections (same speaker discussing related topics)
    speaker_connections = _create_speaker_based_links(units_data)
    links.extend(speaker_connections)

    return links


def _create_theme_based_links(units_data):
    """Create links between units sharing themes."""
    from collections import defaultdict

    theme_groups = defaultdict(list)

    # Group units by themes
    for unit in units_data:
        themes = unit.get("themes", [])
        for theme in themes[:2]:  # Only consider top 2 themes to avoid over-connection
            if theme and len(theme.strip()) > 0:
                theme_groups[theme].append(unit)

    links = []

    # Create star patterns for each theme group
    for theme, units in theme_groups.items():
        if len(units) < 2:
            continue

        # Find the most central unit (earliest in time or most themes)
        central_unit = min(units, key=lambda u: u.get("start_time", 0))

        # Connect other units to central unit (max 3 connections per theme)
        connected_count = 0
        for unit in units:
            if unit != central_unit and connected_count < 3:
                links.append({
                    "source": central_unit["id"],
                    "target": unit["id"],
                    "color": "#888888",  # Dimmer for theme connections
                    "width": 0.8,
                    "relationship": f"theme:{theme}"
                })
                connected_count += 1

    return links


def _create_speaker_based_links(units_data):
    """Create links between units from the same speaker."""
    from collections import defaultdict

    speaker_groups = defaultdict(list)

    # Group by speaker
    for unit in units_data:
        speaker = unit.get("primary_speaker", "Unknown")
        if speaker != "Unknown":
            speaker_groups[speaker].append(unit)

    links = []

    # Create minimal connections within speaker groups
    for speaker, units in speaker_groups.items():
        if len(units) < 2:
            continue

        # Sort by time
        units.sort(key=lambda u: u.get("start_time", 0))

        # Connect consecutive units from same speaker (max 2 connections)
        for i in range(min(2, len(units) - 1)):
            links.append({
                "source": units[i]["id"],
                "target": units[i + 1]["id"],
                "color": "#666666",  # Even dimmer for speaker connections
                "width": 0.6,
                "relationship": f"speaker:{speaker}"
            })

    return links


def _get_content_type_distribution(nodes):
    """Get distribution of content types for filtering."""
    from collections import Counter
    content_types = [node["content_type"] for node in nodes]
    return dict(Counter(content_types))


def _get_theme_distribution(units_data):
    """Get theme distribution for filtering."""
    from collections import Counter
    all_themes = []
    for unit in units_data:
        themes = unit.get("themes", [])
        all_themes.extend(themes[:3])  # Top 3 themes per unit
    return dict(Counter(all_themes))