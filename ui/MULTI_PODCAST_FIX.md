# Multi-Podcast Support Fix

## Issue
The dashboard was not displaying multiple podcasts properly. The backend was hardcoded to return a single generic podcast instead of reading from the actual podcast configuration.

## Root Cause
1. The `/api/v1/podcasts` endpoint in `backend/main.py` was returning a hardcoded single podcast
2. The backend was not reading from the `seeding_pipeline/config/podcasts.yaml` file
3. The meaningful_units API routes were not using podcast-specific database connections

## Changes Made

### 1. Backend Main API (`backend/main.py`)
- Added YAML configuration loading
- Modified `/api/v1/podcasts` endpoint to:
  - Read from `seeding_pipeline/config/podcasts.yaml`
  - Return all enabled podcasts with their metadata
  - Connect to each podcast's specific database to get episode counts
  - Handle database connection failures gracefully

### 2. Meaningful Units API (`backend/api/routes/meaningful_units.py`)
- Added podcast configuration loading
- Modified `get_db_driver()` to accept `podcast_id` parameter
- Updated to use podcast-specific database connections based on configuration

### 3. Frontend Dashboard (`frontend/src/pages/Dashboard.jsx`)
- Enhanced podcast cards to display:
  - Description (if available)
  - Host information
  - Proper "Not loaded" text for podcasts without database data
- Added null checks for missing data

### 4. CSS Styling (`frontend/src/index.css`)
- Added `.description` class for podcast descriptions
- Limited description to 2 lines with ellipsis overflow

## Configuration
The system now reads from `seeding_pipeline/config/podcasts.yaml` which contains:
- The Mel Robbins Podcast (port 7687)
- My First Million (port 7688)

Each podcast has its own Neo4j database instance with different ports.

## Testing
1. Install backend dependencies: `cd backend && pip install -r requirements.txt`
2. Run the servers: `./run_servers.sh`
3. Test the backend: `python test_backend.py`
4. Access the UI at http://localhost:3000

## Expected Result
The dashboard should now display multiple podcast cards, each showing:
- Podcast name
- Description
- Episode count (if database is accessible)
- Last updated date
- Host information