{"permissions": {"allow": ["<PERSON><PERSON>(git clone:*)", "Bash(find:*)", "Bash(pip3 list:*)", "Bash(if [ -d venv ])", "Bash(then source venv/bin/activate)", "Bash(pytest:*)", "Bash(else python3 -m pytest --version)", "Bash(fi)", "Bash(ls:*)", "<PERSON><PERSON>(python3:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON>sh(coverage --version)", "Bash(grep:*)", "Bash(rm:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(diff:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git restore:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git commit:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(test:*)", "Bash(for f in tests/processing/test_vtt_*.py tests/integration/test_vtt_*.py tests/performance/test_vtt_*.py)", "Bash(do echo \"$f: $(grep -c \"def test_\" \"$f\") tests\")", "Bash(done)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(if [ -f \"/home/<USER>/podcastknowledge/seeding_pipeline/tests/processing/test_schemaless_preprocessor.py\" ])", "Bash(then mv \"/home/<USER>/podcastknowledge/seeding_pipeline/tests/processing/test_schemaless_preprocessor.py\" \"/home/<USER>/podcastknowledge/seeding_pipeline/tests/processing/test_preprocessor.py\")", "Bash(else echo \"Test file does not exist\")", "<PERSON><PERSON>(sed:*)", "Bash(rg:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(touch:*)", "Bash(tree:*)", "Bash(git reset:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(systemctl status:*)", "Bash(dpkg:*)", "<PERSON><PERSON>(snap list:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(sudo sh:*)", "<PERSON>sh(sudo usermod:*)", "<PERSON><PERSON>(sudo docker run:*)", "<PERSON><PERSON>(sudo docker:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(yamllint:*)", "Bash(timeout 30 pytest -v --cov=src --cov-report=xml --cov-report=html tests/unit/test_config.py)", "Bash(./scripts/run_tests.py:*)", "Bash(./scripts/view_test_results.py)", "Bash(./scripts/manage_failures.py summary)", "Bash(./scripts/manage_known_issues.py list:*)", "Bash(for f in tests/test_*.py)", "Bash(do echo \"=== $f ===\")", "Bash(git rm:*)", "Bash(git tag:*)", "Bash(./add_podcast.sh:*)", "Bash(./run_pipeline.sh:*)", "Bash(./list_podcasts.sh:*)", "Bash(PROJECT_ROOT=\"$PWD\" python3 -c \"\nimport yaml\nconfig_file = 'seeding_pipeline/config/podcasts.yaml'\nwith open(config_file, 'r') as f:\n    data = yaml.safe_load(f)\nfor podcast in data.get('podcasts', []):\n    if podcast.get('name') == 'The Mel Robbins Podcast':\n        print(podcast.get('rss_feed_url'))\n        break\n\")", "Bash(NEO4J_URI=neo4j://localhost:7688 python3 scripts/speaker_summary_report.py)", "Bash(NEO4J_URI=neo4j://localhost:7688 python3 scripts/speaker_table_report.py)", "Bash(npm create:*)", "Bash(./analyze_podcast_nodes.sh:*)", "Ba<PERSON>(unzip:*)", "WebFetch(domain:infranodus.com)", "WebFetch(domain:noduslabs.com)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(./scripts/setup/manage_ui_servers.sh:*)", "Bash(ss:*)", "Bash(npm install:*)", "Bash(awk:*)", "Bash(node:*)", "Bash(npm bin:*)", "Bash(npm config:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude --version)", "<PERSON><PERSON>(claude code --mcp-timeout 30000)", "Bash(MCP_TIMEOUT=30000 claude code --debug)", "Bash(npx @modelcontextprotocol/server-puppeteer:*)", "Bash(claude code:*)", "Bash(/usr/local/bin/mcp-server-puppeteer:*)", "Bash(npx:*)", "Bash(npm:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm search:*)", "WebFetch(domain:www.sigmajs.org)", "Bash(./ui/scripts/setup/manage_ui_servers.sh:*)", "Bash(sudo kill:*)", "Bash(./run_servers.sh:*)", "Bash(./simple_run.sh)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(arch:*)", "Bash(bash:*)", "WebFetch(domain:github.com)", "Bash(./scripts/setup/install.sh:*)", "WebFetch(domain:vasturiano.github.io)"]}, "enableAllProjectMcpServers": false}